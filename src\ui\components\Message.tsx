/**
 * Message Component - 消息组件
 * 
 * 显示聊天消息，支持不同类型的消息和交互
 */

import React, { useState } from 'react';
import { MessageProps } from '../types';
import { useThemeStyles } from '../theme/ThemeProvider';
import { Button, IconButton } from './Button';
import { CodeBlock } from './CodeBlock';
import { TypewriterText } from './TypewriterText';
import { Transition } from './Transition';
import { Easing } from '../utils/AnimationManager';
import { useAccessibility, useAriaState } from '../accessibility/useAccessibility';

export const Message: React.FC<MessageProps> = ({
  message,
  isStreaming = false,
  onCodeCopy,
  onCodeApply,
  onRetry,
}) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const [isExpanded, setIsExpanded] = useState(true);

  // 可访问性增强
  const { elementRef, state, announce } = useAccessibility({
    enableFocusManagement: true,
    enableAnnouncements: true,
    role: 'article',
    ariaLabel: `${message.role === 'user' ? '用户' : 'AI助手'}消息`,
  });

  const { elementRef: expandButtonRef, ariaState, updateAriaState } = useAriaState({
    expanded: isExpanded,
    controls: `message-content-${message.id || Date.now()}`,
  });

  const isUser = message.type === 'user';
  const isAssistant = message.type === 'assistant';
  const isSystem = message.type === 'system';
  const isTool = message.type === 'tool';

  const getMessageStyles = () => {
    const baseStyles: React.CSSProperties = {
      display: 'flex',
      flexDirection: 'column',
      gap: spacing.sm,
      padding: spacing.md,
      borderRadius: borderRadius.lg,
      marginBottom: spacing.md,
      position: 'relative',
      maxWidth: '100%',
      wordWrap: 'break-word',
    };

    if (isUser) {
      return {
        ...baseStyles,
        backgroundColor: colors.primary,
        color: colors.background,
        alignSelf: 'flex-end',
        marginLeft: spacing.xl,
      };
    }

    if (isAssistant) {
      return {
        ...baseStyles,
        backgroundColor: colors.surface,
        color: colors.text,
        border: `1px solid ${colors.border}`,
        alignSelf: 'flex-start',
        marginRight: spacing.xl,
      };
    }

    if (isSystem) {
      return {
        ...baseStyles,
        backgroundColor: colors.info,
        color: colors.background,
        alignSelf: 'center',
        fontSize: typography.fontSize.sm,
        fontStyle: 'italic',
        margin: `${spacing.sm} ${spacing.xl}`,
      };
    }

    if (isTool) {
      return {
        ...baseStyles,
        backgroundColor: colors.accent,
        color: colors.background,
        alignSelf: 'flex-start',
        marginRight: spacing.xl,
        fontSize: typography.fontSize.sm,
      };
    }

    return baseStyles;
  };

  const getAvatarStyles = (): React.CSSProperties => ({
    width: '32px',
    height: '32px',
    borderRadius: '50%',
    backgroundColor: isUser ? colors.primary : colors.secondary,
    color: colors.background,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    flexShrink: 0,
  });

  const getTimestampStyles = (): React.CSSProperties => ({
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  });

  const formatTimestamp = (timestamp: Date | number) => {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderContent = () => {
    if (typeof message.content === 'string') {
      return renderTextContent(message.content);
    }

    // 处理复杂内容类型
    if (Array.isArray(message.content)) {
      return message.content.map((item: any, index: number) => (
        <Transition
          key={index}
          show={true}
          config={{
            type: 'slide-up',
            duration: 300,
            delay: index * 100,
            easing: Easing.easeOutCubic,
          }}
        >
          <div>{renderContentItem(item)}</div>
        </Transition>
      ));
    }

    return <div>{JSON.stringify(message.content)}</div>;
  };

  const renderTextContent = (text: string) => {
    // 检测代码块
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(text)) !== null) {
      // 添加代码块前的文本
      if (match.index > lastIndex) {
        const textContent = text.slice(lastIndex, match.index);
        parts.push(
          <TypewriterText
            key={`text-${lastIndex}`}
            text={textContent}
            speed={isStreaming ? 50 : 200} // 流式时较慢，非流式时较快
            style={{ whiteSpace: 'pre-wrap' }}
            enableMarkdown={true}
            streamMode={isStreaming}
          />
        );
      }

      // 添加代码块
      const language = match[1] || 'text';
      const code = match[2];
      parts.push(
        <CodeBlock
          key={`code-${match.index}`}
          code={code}
          language={language}
          onCopy={onCodeCopy}
          onApply={onCodeApply}
        />
      );

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余文本
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      parts.push(
        <TypewriterText
          key={`text-${lastIndex}`}
          text={remainingText}
          speed={isStreaming ? 50 : 200}
          style={{ whiteSpace: 'pre-wrap' }}
          enableMarkdown={true}
          streamMode={isStreaming}
        />
      );
    }

    return parts.length > 0 ? parts : (
      <TypewriterText
        text={text}
        speed={isStreaming ? 50 : 200}
        style={{ whiteSpace: 'pre-wrap' }}
        enableMarkdown={true}
        streamMode={isStreaming}
      />
    );
  };

  const renderContentItem = (item: any) => {
    if (typeof item === 'string') {
      return renderTextContent(item);
    }

    if (item.type === 'code') {
      return (
        <CodeBlock
          code={item.content}
          language={item.language || 'text'}
          filename={item.filename}
          onCopy={onCodeCopy}
          onApply={onCodeApply}
        />
      );
    }

    if (item.type === 'image') {
      return (
        <img
          src={item.url}
          alt={item.alt || 'Image'}
          style={{
            maxWidth: '100%',
            borderRadius: borderRadius.md,
            marginTop: spacing.sm,
          }}
        />
      );
    }

    return <div>{JSON.stringify(item)}</div>;
  };

  const renderToolCalls = () => {
    if (!message.toolCalls || message.toolCalls.length === 0) {
      return null;
    }

    return (
      <div style={{ marginTop: spacing.sm }}>
        <div
          style={{
            fontSize: typography.fontSize.sm,
            color: colors.textSecondary,
            marginBottom: spacing.xs,
          }}
        >
          Tool Calls:
        </div>
        {message.toolCalls.map((toolCall, index) => (
          <div
            key={index}
            style={{
              backgroundColor: colors.background,
              border: `1px solid ${colors.border}`,
              borderRadius: borderRadius.sm,
              padding: spacing.sm,
              marginBottom: spacing.xs,
              fontSize: typography.fontSize.sm,
            }}
          >
            <div style={{ fontWeight: typography.fontWeight.bold }}>
              {toolCall.name}
            </div>
            <div style={{ color: colors.textSecondary, marginTop: spacing.xs }}>
              {JSON.stringify(toolCall.arguments, null, 2)}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderActions = () => {
    if (isUser || isSystem) return null;

    return (
      <div
        style={{
          display: 'flex',
          gap: spacing.xs,
          marginTop: spacing.sm,
          opacity: 0.7,
        }}
      >
        {onRetry && (
          <IconButton
            icon="↻"
            size="sm"
            variant="ghost"
            aria-label="Retry"
            onClick={() => onRetry(message.id || '')}
          />
        )}
        <IconButton
          icon="📋"
          size="sm"
          variant="ghost"
          aria-label="Copy"
          onClick={() => {
            if (typeof message.content === 'string') {
              navigator.clipboard.writeText(message.content);
            }
          }}
        />
        <IconButton
          icon={isExpanded ? "−" : "+"}
          size="sm"
          variant="ghost"
          aria-label={isExpanded ? "Collapse" : "Expand"}
          onClick={() => setIsExpanded(!isExpanded)}
        />
      </div>
    );
  };

  const containerStyles: React.CSSProperties = {
    display: 'flex',
    gap: spacing.sm,
    alignItems: 'flex-start',
    width: '100%',
  };

  return (
    <Transition
      show={true}
      config={{
        type: 'slide-up',
        duration: 400,
        easing: Easing.easeOutCubic,
      }}
    >
      <div style={containerStyles}>
        {!isUser && (
          <Transition
            show={true}
            config={{
              type: 'scale-fade',
              duration: 300,
              delay: 100,
              easing: Easing.easeOutBack,
            }}
          >
            <div style={getAvatarStyles()}>
              {isAssistant ? '🤖' : isSystem ? '⚙️' : isTool ? '🔧' : '?'}
            </div>
          </Transition>
        )}
      
      <div style={getMessageStyles()}>
        {isExpanded && (
          <>
            {renderContent()}
            {renderToolCalls()}
            {isStreaming && (
              <TypewriterText
                text=""
                cursor={true}
                cursorChar="▋"
                cursorBlinkSpeed={600}
                style={{
                  marginLeft: spacing.xs,
                  color: colors.primary,
                }}
              />
            )}
          </>
        )}
        
        <div style={getTimestampStyles()}>
          {formatTimestamp(message.timestamp)}
        </div>
        
        {renderActions()}
      </div>

        {isUser && (
          <Transition
            show={true}
            config={{
              type: 'scale-fade',
              duration: 300,
              delay: 100,
              easing: Easing.easeOutBack,
            }}
          >
            <div style={getAvatarStyles()}>
              👤
            </div>
          </Transition>
        )}
      </div>
    </Transition>
  );
};

// 消息列表组件
interface MessageListProps {
  messages: MessageProps['message'][];
  isStreaming?: boolean;
  onCodeCopy?: (code: string) => void;
  onCodeApply?: (code: string, filename?: string) => void;
  onRetry?: (messageId: string) => void;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  isStreaming,
  onCodeCopy,
  onCodeApply,
  onRetry,
}) => {
  const { spacing } = useThemeStyles();

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: spacing.sm,
        padding: spacing.md,
        height: '100%',
        overflowY: 'auto',
      }}
    >
      {messages.map((message, index) => (
        <Message
          key={message.id}
          message={message}
          isStreaming={isStreaming && index === messages.length - 1}
          onCodeCopy={onCodeCopy}
          onCodeApply={onCodeApply}
          onRetry={onRetry}
        />
      ))}
    </div>
  );
};
