/**
 * UI Types and Interfaces
 * 
 * 定义UI组件的类型和接口
 */

import { Message, CodeBlock, Theme } from '@/types';

// 主题相关类型
export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  accent: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface ThemeConfig {
  name: Theme;
  colors: ThemeColors;
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    fontWeight: {
      normal: number;
      medium: number;
      bold: number;
    };
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

// 消息组件相关类型
export interface MessageProps {
  message: Message;
  isStreaming?: boolean;
  onCodeCopy?: (code: string) => void;
  onCodeApply?: (code: string, filename?: string) => void;
  onRetry?: (messageId: string) => void;
}

export interface CodeBlockProps {
  code: string;
  language: string;
  filename?: string;
  showLineNumbers?: boolean;
  onCopy?: (code: string) => void;
  onApply?: (code: string, filename?: string) => void;
}

// 聊天界面相关类型
export interface ChatProps {
  messages: Message[];
  isLoading?: boolean;
  isStreaming?: boolean;
  onSendMessage: (content: string) => void;
  onRetry?: (messageId: string) => void;
  onClear?: () => void;
  placeholder?: string;
  disabled?: boolean;
}

export interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  maxLength?: number;
  multiline?: boolean;
}

// 侧边栏相关类型
export interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

export interface SidebarItemProps {
  icon?: React.ReactNode;
  label: string;
  active?: boolean;
  onClick?: () => void;
  badge?: string | number;
}

// 状态栏相关类型
export interface StatusBarProps {
  model?: string;
  provider?: string;
  tokens?: number;
  cost?: number;
  status?: 'idle' | 'loading' | 'error' | 'success';
  onModelChange?: (model: string) => void;
}

// 设置面板相关类型
export interface SettingsProps {
  isOpen: boolean;
  onClose: () => void;
  settings: SettingsData;
  onSettingsChange: (settings: Partial<SettingsData>) => void;
}

export interface SettingsData {
  theme: Theme;
  fontSize: number;
  showLineNumbers: boolean;
  wordWrap: boolean;
  autoSave: boolean;
  model: string;
  provider: string;
  temperature: number;
  maxTokens: number;
}

// 工具栏相关类型
export interface ToolbarProps {
  onNewChat?: () => void;
  onClearChat?: () => void;
  onSettings?: () => void;
  onExport?: () => void;
  onImport?: () => void;
  disabled?: boolean;
}

// 加载状态相关类型
export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  overlay?: boolean;
}

export interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  variant?: 'text' | 'rectangular' | 'circular';
  animation?: 'pulse' | 'wave' | false;
}

// 模态框相关类型
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

// 通知相关类型
export interface NotificationProps {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

export interface NotificationContextType {
  notifications: NotificationProps[];
  addNotification: (notification: Omit<NotificationProps, 'id' | 'onClose'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

// 快捷键相关类型
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
}

// 拖拽相关类型
export interface DragDropProps {
  onDrop: (files: File[]) => void;
  accept?: string[];
  multiple?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
}

// 虚拟化列表相关类型
export interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
}

// 搜索相关类型
export interface SearchProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (value: string) => void;
  placeholder?: string;
  suggestions?: string[];
  onSuggestionSelect?: (suggestion: string) => void;
  loading?: boolean;
}

// 标签页相关类型
export interface TabProps {
  id: string;
  label: string;
  content: React.ReactNode;
  closable?: boolean;
  icon?: React.ReactNode;
}

export interface TabsProps {
  tabs: TabProps[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  onTabClose?: (tabId: string) => void;
  onTabAdd?: () => void;
}

// 上下文菜单相关类型
export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  shortcut?: string;
  disabled?: boolean;
  separator?: boolean;
  submenu?: ContextMenuItem[];
  onClick?: () => void;
}

export interface ContextMenuProps {
  items: ContextMenuItem[];
  x: number;
  y: number;
  onClose: () => void;
}

// 响应式相关类型
export interface Breakpoints {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
}

export interface ResponsiveProps {
  breakpoints?: Breakpoints;
  children: React.ReactNode;
}

// 动画相关类型
export interface AnimationProps {
  duration?: number;
  delay?: number;
  easing?: string;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  iterationCount?: number | 'infinite';
}

// 表单相关类型
export interface FormFieldProps {
  label?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
}

export interface InputProps {
  type?: 'text' | 'password' | 'email' | 'number' | 'url';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  style?: React.CSSProperties;
  'aria-label'?: string;
  className?: string;
  ref?: React.Ref<HTMLButtonElement>;
}

// 布局相关类型
export interface LayoutProps {
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  children: React.ReactNode;
}

export interface GridProps {
  columns?: number;
  gap?: string;
  children: React.ReactNode;
}

export interface FlexProps {
  direction?: 'row' | 'column';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  align?: 'start' | 'center' | 'end' | 'stretch';
  wrap?: boolean;
  gap?: string;
  children: React.ReactNode;
}
