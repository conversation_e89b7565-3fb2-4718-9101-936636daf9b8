/**
 * 动画效果演示页面
 * 
 * 用于测试和展示各种动画效果的性能和视觉效果
 */

import React, { useState, useCallback } from 'react';
import { useThemeStyles } from '../theme/ThemeProvider';
import { TypewriterText } from '../components/TypewriterText';
import { Transition } from '../components/Transition';
import { PerformanceMonitor, usePerformanceMonitor } from '../components/PerformanceMonitor';
import { Button } from '../components/Button';
import { Easing } from '../utils/AnimationManager';

export const AnimationDemo: React.FC = () => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const { performanceData, isOptimal, handlePerformanceChange } = usePerformanceMonitor();
  
  const [showTypewriter, setShowTypewriter] = useState(false);
  const [showTransitions, setShowTransitions] = useState(false);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(true);
  const [typewriterText, setTypewriterText] = useState('');
  const [transitionType, setTransitionType] = useState<'fade' | 'slide-up' | 'scale' | 'bounce'>('fade');

  // 示例文本
  const sampleTexts = [
    '这是一个简单的打字机效果演示。',
    '支持**粗体**和*斜体*的Markdown格式。',
    '还可以显示`代码片段`和其他格式化文本。',
    '动画效果流畅，性能优化良好，确保60fps的渲染性能。',
  ];

  const longText = `# AI编程助手动画系统

这是一个高性能的动画系统演示，专为AI编程助手设计。

## 主要特性

- **60fps流畅动画**: 使用requestAnimationFrame优化动画循环
- **智能性能监控**: 实时监控FPS、内存使用和交互延迟
- **优雅降级**: 根据设备性能自动调整动画复杂度
- **可访问性支持**: 遵循WCAG 2.1标准，支持减少动画偏好

## 技术实现

\`\`\`typescript
// 高性能动画管理器
const animationManager = new AnimationManager();

// 创建动画
animationManager.animate('my-animation', from, to, {
  duration: 300,
  easing: Easing.easeOutCubic,
  onUpdate: (progress, value) => {
    // 更新UI
  }
});
\`\`\`

这个系统确保了在各种设备上都能提供流畅的用户体验。`;

  const handleTypewriterDemo = useCallback((textIndex: number) => {
    setTypewriterText(sampleTexts[textIndex] || sampleTexts[0]);
    setShowTypewriter(true);
  }, [sampleTexts]);

  const handleLongTextDemo = useCallback(() => {
    setTypewriterText(longText);
    setShowTypewriter(true);
  }, [longText]);

  const containerStyles: React.CSSProperties = {
    padding: spacing.lg,
    maxWidth: '800px',
    margin: '0 auto',
    fontFamily: typography.fontFamily,
    color: colors.text,
    backgroundColor: colors.background,
    minHeight: '100vh',
  };

  const sectionStyles: React.CSSProperties = {
    marginBottom: spacing.xl,
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.border}`,
  };

  const titleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xl,
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: colors.text,
  };

  const demoAreaStyles: React.CSSProperties = {
    minHeight: '200px',
    padding: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    border: `1px solid ${colors.border}`,
    marginBottom: spacing.md,
  };

  const controlsStyles: React.CSSProperties = {
    display: 'flex',
    gap: spacing.sm,
    flexWrap: 'wrap',
    marginBottom: spacing.md,
  };

  const statusStyles: React.CSSProperties = {
    padding: spacing.sm,
    borderRadius: borderRadius.sm,
    backgroundColor: isOptimal ? colors.success : colors.warning,
    color: 'white',
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.md,
  };

  return (
    <div style={containerStyles}>
      <h1 style={titleStyles}>AI编程助手 - 动画效果演示</h1>
      
      {/* 性能状态 */}
      {performanceData && (
        <div style={statusStyles}>
          性能状态: {isOptimal ? '优秀' : '需要优化'} | 
          FPS: {performanceData.fps} | 
          交互延迟: {performanceData.interactionDelay.toFixed(1)}ms
        </div>
      )}

      {/* 打字机效果演示 */}
      <section style={sectionStyles}>
        <h2 style={titleStyles}>打字机效果演示</h2>
        
        <div style={controlsStyles}>
          {sampleTexts.map((_, index) => (
            <Button
              key={index}
              onClick={() => handleTypewriterDemo(index)}
              variant="outline"
              size="sm"
            >
              示例 {index + 1}
            </Button>
          ))}
          <Button
            onClick={handleLongTextDemo}
            variant="outline"
            size="sm"
          >
            长文本演示
          </Button>
          <Button
            onClick={() => setShowTypewriter(false)}
            variant="ghost"
            size="sm"
          >
            清除
          </Button>
        </div>
        
        <div style={demoAreaStyles}>
          {showTypewriter && typewriterText && (
            <TypewriterText
              text={typewriterText}
              speed={50}
              cursor={true}
              cursorChar="▋"
              enableMarkdown={true}
              onComplete={() => console.log('打字机效果完成')}
              onCharacterTyped={(char, index) => {
                if (index % 10 === 0) {
                  console.log(`已输入 ${index + 1} 个字符`);
                }
              }}
            />
          )}
        </div>
      </section>

      {/* 过渡动画演示 */}
      <section style={sectionStyles}>
        <h2 style={titleStyles}>过渡动画演示</h2>
        
        <div style={controlsStyles}>
          <Button
            onClick={() => setShowTransitions(!showTransitions)}
            variant="primary"
            size="sm"
          >
            {showTransitions ? '隐藏' : '显示'}动画
          </Button>
          
          <select
            value={transitionType}
            onChange={(e) => setTransitionType(e.target.value as any)}
            style={{
              padding: spacing.xs,
              borderRadius: borderRadius.sm,
              border: `1px solid ${colors.border}`,
              backgroundColor: colors.surface,
              color: colors.text,
            }}
          >
            <option value="fade">淡入淡出</option>
            <option value="slide-up">向上滑动</option>
            <option value="scale">缩放</option>
            <option value="bounce">弹跳</option>
          </select>
        </div>
        
        <div style={demoAreaStyles}>
          <Transition
            show={showTransitions}
            config={{
              type: transitionType,
              duration: 500,
              easing: transitionType === 'bounce' ? Easing.easeOutBounce : Easing.easeOutCubic,
            }}
          >
            <div style={{
              padding: spacing.lg,
              backgroundColor: colors.primary,
              color: 'white',
              borderRadius: borderRadius.md,
              textAlign: 'center',
              fontSize: typography.fontSize.lg,
            }}>
              这是一个过渡动画演示框
              <br />
              <small>动画类型: {transitionType}</small>
            </div>
          </Transition>
        </div>
      </section>

      {/* 性能监控 */}
      <section style={sectionStyles}>
        <h2 style={titleStyles}>性能监控</h2>
        
        <div style={controlsStyles}>
          <Button
            onClick={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
            variant="outline"
            size="sm"
          >
            {showPerformanceMonitor ? '隐藏' : '显示'}性能监控器
          </Button>
        </div>
        
        <p style={{ color: colors.textSecondary, marginBottom: spacing.md }}>
          性能监控器显示在页面右上角，实时监控动画性能指标。
          包括FPS、帧时间、交互延迟、内存使用等关键指标。
        </p>
        
        {performanceData && (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: spacing.sm,
          }}>
            <div style={{
              padding: spacing.sm,
              backgroundColor: colors.background,
              borderRadius: borderRadius.sm,
              textAlign: 'center',
            }}>
              <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.primary }}>
                {performanceData.fps}
              </div>
              <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
                当前FPS
              </div>
            </div>
            
            <div style={{
              padding: spacing.sm,
              backgroundColor: colors.background,
              borderRadius: borderRadius.sm,
              textAlign: 'center',
            }}>
              <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.primary }}>
                {performanceData.interactionDelay.toFixed(1)}ms
              </div>
              <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
                交互延迟
              </div>
            </div>
            
            <div style={{
              padding: spacing.sm,
              backgroundColor: colors.background,
              borderRadius: borderRadius.sm,
              textAlign: 'center',
            }}>
              <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.primary }}>
                {performanceData.animationCount}
              </div>
              <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
                活动动画
              </div>
            </div>
          </div>
        )}
      </section>

      {/* 性能监控组件 */}
      <PerformanceMonitor
        show={showPerformanceMonitor}
        position="top-right"
        onPerformanceChange={handlePerformanceChange}
      />
    </div>
  );
};
