/**
 * 懒加载的代码编辑器组件
 * 
 * 这是一个重量级组件，使用懒加载优化初始包大小
 */

import React, { useState, useEffect, useRef } from 'react';
import { useThemeStyles } from '../../theme/ThemeProvider';
import { monacoThemeAdapter } from '../../theme/MonacoThemeAdapter';

export interface LazyCodeEditorProps {
  value?: string;
  language?: string;
  theme?: string;
  readOnly?: boolean;
  minimap?: boolean;
  lineNumbers?: boolean;
  wordWrap?: boolean;
  fontSize?: number;
  tabSize?: number;
  onChange?: (value: string) => void;
  onMount?: (editor: any, monaco: any) => void;
  className?: string;
  style?: React.CSSProperties;
}

export const LazyCodeEditor: React.FC<LazyCodeEditorProps> = ({
  value = '',
  language = 'typescript',
  theme,
  readOnly = false,
  minimap = true,
  lineNumbers = true,
  wordWrap = false,
  fontSize = 14,
  tabSize = 2,
  onChange,
  onMount,
  className,
  style,
}) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();

  // 创建兼容性对象以支持现有的themeConfig引用
  const themeConfig = {
    name: 'dark', // 默认主题名称
    colors,
    spacing,
    typography,
    borderRadius,
    shadows: {
      sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
      xl: '0 20px 25px rgba(0, 0, 0, 0.1)'
    }
  };
  const [monaco, setMonaco] = useState<any>(null);
  const [editor, setEditor] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 动态加载Monaco Editor
  useEffect(() => {
    let isMounted = true;

    const loadMonaco = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 动态导入Monaco Editor
        const monacoModule = await import('monaco-editor');
        
        if (!isMounted) return;

        // 配置Monaco Editor
        monacoModule.editor.defineTheme('ai-agent-theme', {
          base: themeConfig.name === 'light' ? 'vs' : 'vs-dark',
          inherit: true,
          rules: [],
          colors: {
            'editor.background': themeConfig.colors.background,
            'editor.foreground': themeConfig.colors.text,
          },
        });

        setMonaco(monacoModule);
        setIsLoading(false);
      } catch (err) {
        if (!isMounted) return;
        
        console.error('Failed to load Monaco Editor:', err);
        setError('代码编辑器加载失败');
        setIsLoading(false);
      }
    };

    loadMonaco();

    return () => {
      isMounted = false;
    };
  }, [themeConfig]);

  // 创建编辑器实例
  useEffect(() => {
    if (!monaco || !containerRef.current || editor) return;

    try {
      const editorInstance = monaco.editor.create(containerRef.current, {
        value,
        language,
        theme: theme || 'ai-agent-theme',
        readOnly,
        minimap: { enabled: minimap },
        lineNumbers: lineNumbers ? 'on' : 'off',
        wordWrap: wordWrap ? 'on' : 'off',
        fontSize,
        tabSize,
        automaticLayout: true,
        scrollBeyondLastLine: false,
        renderWhitespace: 'selection',
        renderControlCharacters: true,
        fontFamily: themeConfig.typography.fontFamily,
        lineHeight: 1.5,
        letterSpacing: 0.5,
        smoothScrolling: true,
        cursorBlinking: 'smooth',
        cursorSmoothCaretAnimation: true,
        contextmenu: true,
        mouseWheelZoom: true,
        quickSuggestions: {
          other: true,
          comments: true,
          strings: true,
        },
        suggestOnTriggerCharacters: true,
        acceptSuggestionOnEnter: 'on',
        acceptSuggestionOnCommitCharacter: true,
        snippetSuggestions: 'top',
        emptySelectionClipboard: false,
        copyWithSyntaxHighlighting: true,
        useTabStops: true,
        wordBasedSuggestions: true,
        parameterHints: {
          enabled: true,
          cycle: true,
        },
        hover: {
          enabled: true,
          delay: 300,
          sticky: true,
        },
        folding: true,
        foldingStrategy: 'auto',
        showFoldingControls: 'mouseover',
        unfoldOnClickAfterEndOfLine: false,
        bracketPairColorization: {
          enabled: true,
        },
        guides: {
          bracketPairs: true,
          bracketPairsHorizontal: true,
          highlightActiveBracketPair: true,
          indentation: true,
          highlightActiveIndentation: true,
        },
        lightbulb: {
          enabled: true,
        },
        codeActionsOnSave: {
          'source.organizeImports': true,
        },
      });

      // 监听值变化
      editorInstance.onDidChangeModelContent(() => {
        const newValue = editorInstance.getValue();
        onChange?.(newValue);
      });

      setEditor(editorInstance);
      onMount?.(editorInstance, monaco);
    } catch (err) {
      console.error('Failed to create Monaco Editor instance:', err);
      setError('编辑器初始化失败');
    }
  }, [monaco, value, language, theme, readOnly, minimap, lineNumbers, wordWrap, fontSize, tabSize, onChange, onMount, themeConfig]);

  // 更新编辑器值
  useEffect(() => {
    if (editor && editor.getValue() !== value) {
      editor.setValue(value);
    }
  }, [editor, value]);

  // 更新编辑器主题
  useEffect(() => {
    if (monaco && editor) {
      const themeName = `ai-agent-${themeConfig.name}`;
      monacoThemeAdapter.registerMonacoTheme(monaco, themeName, themeConfig);
      monaco.editor.setTheme(themeName);
    }
  }, [monaco, editor, themeConfig]);

  // 清理编辑器
  useEffect(() => {
    return () => {
      if (editor) {
        editor.dispose();
      }
    };
  }, [editor]);

  const containerStyles: React.CSSProperties = {
    width: '100%',
    height: '400px',
    border: `1px solid ${themeConfig.colors.border}`,
    borderRadius: themeConfig.borderRadius.md,
    overflow: 'hidden',
    ...style,
  };

  const loadingStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    backgroundColor: themeConfig.colors.surface,
    color: themeConfig.colors.textSecondary,
    fontSize: themeConfig.typography.fontSize.sm,
  };

  const errorStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    backgroundColor: themeConfig.colors.surface,
    color: themeConfig.colors.error,
    fontSize: themeConfig.typography.fontSize.sm,
    padding: themeConfig.spacing.md,
    textAlign: 'center',
  };

  if (error) {
    return (
      <div className={className} style={containerStyles}>
        <div style={errorStyles}>
          <div style={{ marginBottom: themeConfig.spacing.sm }}>⚠️</div>
          <div>{error}</div>
          <button
            onClick={() => window.location.reload()}
            style={{
              marginTop: themeConfig.spacing.md,
              padding: `${themeConfig.spacing.sm} ${themeConfig.spacing.md}`,
              backgroundColor: themeConfig.colors.primary,
              color: 'white',
              border: 'none',
              borderRadius: themeConfig.borderRadius.sm,
              cursor: 'pointer',
              fontSize: themeConfig.typography.fontSize.sm,
            }}
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={className} style={containerStyles}>
        <div style={loadingStyles}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: themeConfig.spacing.sm,
          }}>
            <div style={{
              width: '16px',
              height: '16px',
              border: `2px solid ${themeConfig.colors.border}`,
              borderTop: `2px solid ${themeConfig.colors.primary}`,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }} />
            正在加载代码编辑器...
          </div>
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}
          </style>
        </div>
      </div>
    );
  }

  return (
    <div className={className} style={containerStyles}>
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};
