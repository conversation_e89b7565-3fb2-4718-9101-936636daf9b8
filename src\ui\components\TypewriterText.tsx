/**
 * 高性能打字机效果组件
 * 
 * 使用requestAnimationFrame优化渲染性能，支持流式文本显示
 * 确保60fps流畅动画和<50ms交互延迟
 */

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { animationManager, Easing } from '../utils/AnimationManager';
import { useThemeStyles } from '../theme/ThemeProvider';

export interface TypewriterTextProps {
  text: string;
  speed?: number; // 字符/秒，默认50
  delay?: number; // 开始延迟，毫秒
  cursor?: boolean; // 是否显示光标
  cursorChar?: string; // 光标字符
  cursorBlinkSpeed?: number; // 光标闪烁速度，毫秒
  onComplete?: () => void;
  onCharacterTyped?: (char: string, index: number) => void;
  className?: string;
  style?: React.CSSProperties;
  preserveWhitespace?: boolean; // 是否保留空白字符
  enableMarkdown?: boolean; // 是否启用Markdown渲染
  streamMode?: boolean; // 流式模式，适用于实时接收的文本
}

interface TypewriterState {
  displayText: string;
  currentIndex: number;
  isComplete: boolean;
  showCursor: boolean;
}

export const TypewriterText: React.FC<TypewriterTextProps> = ({
  text,
  speed = 50,
  delay = 0,
  cursor = true,
  cursorChar = '|',
  cursorBlinkSpeed = 500,
  onComplete,
  onCharacterTyped,
  className,
  style,
  preserveWhitespace = true,
  enableMarkdown = false,
  streamMode = false,
}) => {
  const { colors, typography } = useThemeStyles();
  const [state, setState] = useState<TypewriterState>({
    displayText: '',
    currentIndex: 0,
    isComplete: false,
    showCursor: cursor,
  });
  
  const containerRef = useRef<HTMLDivElement>(null);
  const animationIdRef = useRef<string>('');
  const cursorAnimationIdRef = useRef<string>('');
  const lastTextRef = useRef<string>('');
  const isAnimatingRef = useRef<boolean>(false);

  // 优化的文本分割，考虑Unicode字符
  const textChars = useMemo(() => {
    if (!text) return [];

    // 使用Intl.Segmenter进行正确的字符分割（支持emoji等复合字符）
    if (typeof Intl !== 'undefined' && (Intl as any).Segmenter) {
      const segmenter = new (Intl as any).Segmenter('zh-CN', { granularity: 'grapheme' });
      return Array.from(segmenter.segment(text)).map((segment: any) => segment.segment);
    }

    // 降级方案：简单字符分割
    return Array.from(text);
  }, [text]);

  // 启动打字机动画
  const startTypewriter = useCallback(() => {
    if (isAnimatingRef.current || textChars.length === 0) return;
    
    isAnimatingRef.current = true;
    const animationId = `typewriter-${Date.now()}-${Math.random()}`;
    animationIdRef.current = animationId;
    
    const duration = (textChars.length / speed) * 1000; // 转换为毫秒
    
    animationManager.animate(
      animationId,
      0,
      textChars.length,
      {
        duration,
        delay,
        easing: Easing.linear,
        onUpdate: (progress, value) => {
          const currentIndex = Math.floor(value);
          const displayText = textChars.slice(0, currentIndex).join('');
          
          setState(prev => ({
            ...prev,
            displayText,
            currentIndex,
            isComplete: currentIndex >= textChars.length,
          }));
          
          // 触发字符输入回调
          if (currentIndex > 0 && currentIndex <= textChars.length) {
            const char = textChars[currentIndex - 1];
            onCharacterTyped?.(char, currentIndex - 1);
          }
        },
        onComplete: () => {
          isAnimatingRef.current = false;
          setState(prev => ({
            ...prev,
            isComplete: true,
          }));
          onComplete?.();
        },
        onCancel: () => {
          isAnimatingRef.current = false;
        },
      }
    );
  }, [textChars, speed, delay, onCharacterTyped, onComplete]);

  // 启动光标闪烁动画
  const startCursorBlink = useCallback(() => {
    if (!cursor) return;
    
    const cursorAnimationId = `cursor-${Date.now()}-${Math.random()}`;
    cursorAnimationIdRef.current = cursorAnimationId;
    
    const blinkCycle = () => {
      animationManager.animate(
        cursorAnimationId,
        0,
        1,
        {
          duration: cursorBlinkSpeed,
          easing: Easing.linear,
          onUpdate: (progress) => {
            setState(prev => ({
              ...prev,
              showCursor: progress < 0.5,
            }));
          },
          onComplete: () => {
            // 递归调用实现循环闪烁
            if (cursor && !state.isComplete) {
              setTimeout(blinkCycle, 0);
            }
          },
        }
      );
    };
    
    blinkCycle();
  }, [cursor, cursorBlinkSpeed, state.isComplete]);

  // 停止所有动画
  const stopAnimations = useCallback(() => {
    if (animationIdRef.current) {
      animationManager.cancel(animationIdRef.current);
    }
    if (cursorAnimationIdRef.current) {
      animationManager.cancel(cursorAnimationIdRef.current);
    }
    isAnimatingRef.current = false;
  }, []);

  // 立即完成打字机效果
  const completeImmediately = useCallback(() => {
    stopAnimations();
    setState({
      displayText: text,
      currentIndex: textChars.length,
      isComplete: true,
      showCursor: cursor,
    });
    onComplete?.();
  }, [text, textChars.length, cursor, onComplete, stopAnimations]);

  // 处理文本变化
  useEffect(() => {
    if (text !== lastTextRef.current) {
      lastTextRef.current = text;
      
      if (streamMode && text.startsWith(state.displayText)) {
        // 流式模式：只添加新字符
        const newChars = text.slice(state.displayText.length);
        if (newChars) {
          setState(prev => ({
            ...prev,
            displayText: text,
            currentIndex: textChars.length,
            isComplete: false,
          }));
        }
      } else {
        // 重新开始打字机效果
        stopAnimations();
        setState({
          displayText: '',
          currentIndex: 0,
          isComplete: false,
          showCursor: cursor,
        });
        
        // 延迟启动以确保状态更新
        setTimeout(startTypewriter, 0);
      }
    }
  }, [text, streamMode, state.displayText, textChars.length, cursor, startTypewriter, stopAnimations]);

  // 启动光标闪烁
  useEffect(() => {
    if (cursor) {
      startCursorBlink();
    }
    
    return () => {
      if (cursorAnimationIdRef.current) {
        animationManager.cancel(cursorAnimationIdRef.current);
      }
    };
  }, [cursor, startCursorBlink]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopAnimations();
    };
  }, [stopAnimations]);

  // 渲染文本内容
  const renderContent = () => {
    let content = state.displayText;
    
    if (!preserveWhitespace) {
      content = content.trim();
    }
    
    if (enableMarkdown) {
      // 简单的Markdown渲染（可以后续扩展）
      content = content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>');
      
      return (
        <span
          dangerouslySetInnerHTML={{ __html: content }}
          style={{ whiteSpace: preserveWhitespace ? 'pre-wrap' : 'normal' }}
        />
      );
    }
    
    return (
      <span style={{ whiteSpace: preserveWhitespace ? 'pre-wrap' : 'normal' }}>
        {content}
      </span>
    );
  };

  const containerStyles: React.CSSProperties = {
    fontFamily: typography.fontFamily,
    fontSize: typography.fontSize.base,
    lineHeight: typography.lineHeight.normal,
    color: colors.text,
    display: 'inline-block',
    ...style,
  };

  const cursorStyles: React.CSSProperties = {
    color: colors.primary,
    fontWeight: 'bold',
    animation: 'none', // 使用我们自己的动画管理
    opacity: state.showCursor ? 1 : 0,
    transition: 'opacity 0.1s ease',
  };

  return (
    <div
      ref={containerRef}
      className={className}
      style={containerStyles}
      onClick={completeImmediately} // 点击立即完成
      role="text"
      aria-live="polite"
      aria-label={`正在输入: ${state.displayText}`}
    >
      {renderContent()}
      {cursor && !state.isComplete && (
        <span style={cursorStyles}>
          {cursorChar}
        </span>
      )}
    </div>
  );
};

// 高阶组件：为现有组件添加打字机效果
export const withTypewriter = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return React.forwardRef<any, P & { typewriterProps?: Partial<TypewriterTextProps> }>(
    (props, ref) => {
      const { typewriterProps, ...componentProps } = props;
      
      return (
        <TypewriterText {...typewriterProps}>
          <Component {...(componentProps as P)} ref={ref} />
        </TypewriterText>
      );
    }
  );
};

// Hook：使用打字机效果
export const useTypewriter = (
  text: string,
  options?: Partial<TypewriterTextProps>
) => {
  const [displayText, setDisplayText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  
  const typewriterProps: TypewriterTextProps = {
    text,
    onComplete: () => {
      setIsComplete(true);
      options?.onComplete?.();
    },
    onCharacterTyped: (char, index) => {
      setDisplayText(prev => prev + char);
      options?.onCharacterTyped?.(char, index);
    },
    ...options,
  };
  
  return {
    displayText,
    isComplete,
    typewriterProps,
  };
};
