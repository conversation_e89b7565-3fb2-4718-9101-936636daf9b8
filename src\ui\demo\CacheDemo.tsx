/**
 * 缓存演示页面
 * 
 * 展示智能缓存策略和对话历史管理功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useThemeStyles } from '../theme/ThemeProvider';
import { Button } from '../components/Button';
import { cacheManager } from '../utils/CacheManager';
import { conversationCache, Message, Conversation } from '../utils/ConversationCache';

export const CacheDemo: React.FC = () => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  
  const [cacheStats, setCacheStats] = useState<any>({});
  const [conversationStats, setConversationStats] = useState<any>({});
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string>('');
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 更新统计信息
  const updateStats = useCallback(async () => {
    setCacheStats(cacheManager.getStats());
    setConversationStats(conversationCache.getStats());
    
    const convList = await conversationCache.getConversationList();
    setConversations(convList);
  }, []);

  useEffect(() => {
    updateStats();
    
    // 定期更新统计信息
    const interval = setInterval(updateStats, 2000);
    return () => clearInterval(interval);
  }, [updateStats]);

  const containerStyles: React.CSSProperties = {
    padding: spacing.lg,
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: typography.fontFamily,
    color: colors.text,
    backgroundColor: colors.background,
    minHeight: '100vh',
  };

  const sectionStyles: React.CSSProperties = {
    marginBottom: spacing.xl,
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.border}`,
  };

  const titleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.md,
    color: colors.text,
  };

  const subtitleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
    color: colors.text,
  };

  const statsGridStyles: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
    gap: spacing.md,
    marginBottom: spacing.lg,
  };

  const statCardStyles: React.CSSProperties = {
    padding: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    border: `1px solid ${colors.border}`,
    textAlign: 'center',
  };

  const inputStyles: React.CSSProperties = {
    width: '100%',
    padding: spacing.sm,
    border: `1px solid ${colors.border}`,
    borderRadius: borderRadius.sm,
    backgroundColor: colors.background,
    color: colors.text,
    fontSize: typography.fontSize.sm,
    marginBottom: spacing.sm,
  };

  const messageStyles: React.CSSProperties = {
    padding: spacing.sm,
    marginBottom: spacing.xs,
    borderRadius: borderRadius.sm,
    fontSize: typography.fontSize.sm,
  };

  const userMessageStyles: React.CSSProperties = {
    ...messageStyles,
    backgroundColor: colors.primary,
    color: 'white',
    marginLeft: spacing.lg,
  };

  const assistantMessageStyles: React.CSSProperties = {
    ...messageStyles,
    backgroundColor: colors.surface,
    border: `1px solid ${colors.border}`,
    marginRight: spacing.lg,
  };

  // 创建新对话
  const handleCreateConversation = async () => {
    setIsLoading(true);
    try {
      const conversation = await conversationCache.createConversation(
        `对话 ${conversations.length + 1}`,
        {
          id: Date.now().toString(),
          role: 'system',
          content: '你好！我是AI编程助手，有什么可以帮助你的吗？',
          timestamp: Date.now(),
        }
      );
      
      setSelectedConversation(conversation.id);
      await updateStats();
    } catch (error) {
      console.error('创建对话失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;
    
    setIsLoading(true);
    try {
      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content: newMessage,
        timestamp: Date.now(),
        metadata: { tokens: Math.ceil(newMessage.length / 4) },
      };
      
      await conversationCache.addMessage(selectedConversation, userMessage);
      
      // 模拟AI回复
      setTimeout(async () => {
        const responses = [
          '我理解你的问题，让我来帮助你解决。',
          '这是一个很好的问题，我需要分析一下。',
          '根据你的描述，我建议采用以下方法：',
          '让我为你提供一个详细的解决方案。',
          '这个问题涉及多个方面，我们一步步来看。',
        ];
        
        const response = responses[Math.floor(Math.random() * responses.length)];
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response,
          timestamp: Date.now(),
          metadata: { tokens: Math.ceil(response.length / 4) },
        };
        
        await conversationCache.addMessage(selectedConversation, assistantMessage);
        await updateStats();
        setIsLoading(false);
      }, 1000);
      
      setNewMessage('');
      await updateStats();
    } catch (error) {
      console.error('发送消息失败:', error);
      setIsLoading(false);
    }
  };

  // 搜索对话
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }
    
    try {
      const results = await conversationCache.searchConversations(searchQuery);
      setSearchResults(results);
    } catch (error) {
      console.error('搜索失败:', error);
    }
  };

  // 测试缓存性能
  const handleCacheTest = async () => {
    setIsLoading(true);
    const startTime = performance.now();
    
    try {
      // 写入测试数据
      for (let i = 0; i < 100; i++) {
        await cacheManager.set(`test-${i}`, {
          id: i,
          data: `测试数据 ${i}`,
          timestamp: Date.now(),
          content: 'x'.repeat(1000), // 1KB数据
        });
      }
      
      // 读取测试数据
      for (let i = 0; i < 100; i++) {
        await cacheManager.get(`test-${i}`);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      alert(`缓存性能测试完成！\n写入+读取100个1KB项目耗时: ${duration.toFixed(2)}ms\n平均每项: ${(duration/200).toFixed(2)}ms`);
      
      await updateStats();
    } catch (error) {
      console.error('缓存测试失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 清空缓存
  const handleClearCache = () => {
    cacheManager.clear();
    updateStats();
  };

  // 获取选中的对话
  const getSelectedConversation = () => {
    return conversations.find(c => c.id === selectedConversation);
  };

  return (
    <div style={containerStyles}>
      <h1 style={titleStyles}>AI编程助手 - 缓存演示</h1>
      
      {/* 缓存统计 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>缓存统计</h2>
        <div style={statsGridStyles}>
          <div style={statCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.primary }}>
              {cacheStats.totalEntries || 0}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              缓存项数
            </div>
          </div>
          
          <div style={statCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.success }}>
              {((cacheStats.totalSize || 0) / 1024).toFixed(1)}KB
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              缓存大小
            </div>
          </div>
          
          <div style={statCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.info }}>
              {((cacheStats.hitRate || 0) * 100).toFixed(1)}%
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              命中率
            </div>
          </div>
          
          <div style={statCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.warning }}>
              {(cacheStats.averageAccessTime || 0).toFixed(1)}ms
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              平均访问时间
            </div>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: spacing.sm, flexWrap: 'wrap' }}>
          <Button onClick={handleCacheTest} disabled={isLoading}>
            性能测试
          </Button>
          <Button onClick={handleClearCache} variant="outline">
            清空缓存
          </Button>
        </div>
      </section>

      {/* 对话管理 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>对话管理</h2>
        
        <div style={{ marginBottom: spacing.md }}>
          <div style={statsGridStyles}>
            <div style={statCardStyles}>
              <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.primary }}>
                {conversationStats.conversations || 0}
              </div>
              <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
                对话数量
              </div>
            </div>
            
            <div style={statCardStyles}>
              <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.success }}>
                {conversationStats.contextWindows || 0}
              </div>
              <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
                上下文窗口
              </div>
            </div>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: spacing.sm, marginBottom: spacing.md, flexWrap: 'wrap' }}>
          <Button onClick={handleCreateConversation} disabled={isLoading}>
            创建新对话
          </Button>
          
          <select
            value={selectedConversation}
            onChange={(e) => setSelectedConversation(e.target.value)}
            style={{
              ...inputStyles,
              width: 'auto',
              marginBottom: 0,
            }}
          >
            <option value="">选择对话</option>
            {conversations.map(conv => (
              <option key={conv.id} value={conv.id}>
                {conv.title} ({conv.messages.length}条消息)
              </option>
            ))}
          </select>
        </div>
        
        {/* 对话内容 */}
        {selectedConversation && (
          <div style={{
            border: `1px solid ${colors.border}`,
            borderRadius: borderRadius.md,
            padding: spacing.md,
            marginBottom: spacing.md,
            maxHeight: '300px',
            overflowY: 'auto',
            backgroundColor: colors.background,
          }}>
            {getSelectedConversation()?.messages.map(message => (
              <div
                key={message.id}
                style={message.role === 'user' ? userMessageStyles : assistantMessageStyles}
              >
                <strong>{message.role === 'user' ? '用户' : 'AI助手'}:</strong> {message.content}
                {message.metadata?.tokens && (
                  <span style={{ 
                    fontSize: typography.fontSize.xs, 
                    color: colors.textSecondary,
                    marginLeft: spacing.sm 
                  }}>
                    ({message.metadata.tokens} tokens)
                  </span>
                )}
              </div>
            ))}
          </div>
        )}
        
        {/* 发送消息 */}
        {selectedConversation && (
          <div style={{ display: 'flex', gap: spacing.sm }}>
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="输入消息..."
              style={{ ...inputStyles, marginBottom: 0, flex: 1 }}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            />
            <Button onClick={handleSendMessage} disabled={isLoading || !newMessage.trim()}>
              发送
            </Button>
          </div>
        )}
      </section>

      {/* 搜索功能 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>对话搜索</h2>
        
        <div style={{ display: 'flex', gap: spacing.sm, marginBottom: spacing.md }}>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索对话内容..."
            style={{ ...inputStyles, marginBottom: 0, flex: 1 }}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <Button onClick={handleSearch}>
            搜索
          </Button>
        </div>
        
        {searchResults.length > 0 && (
          <div>
            <h3 style={{ ...subtitleStyles, fontSize: typography.fontSize.md }}>
              搜索结果 ({searchResults.length}个)
            </h3>
            {searchResults.map(conv => (
              <div
                key={conv.id}
                style={{
                  padding: spacing.sm,
                  border: `1px solid ${colors.border}`,
                  borderRadius: borderRadius.sm,
                  marginBottom: spacing.xs,
                  cursor: 'pointer',
                  backgroundColor: selectedConversation === conv.id ? colors.primary + '20' : 'transparent',
                }}
                onClick={() => setSelectedConversation(conv.id)}
              >
                <strong>{conv.title}</strong>
                <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
                  {conv.messages.length}条消息 · {new Date(conv.updatedAt).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  );
};
