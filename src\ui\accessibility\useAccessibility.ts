/**
 * 可访问性React Hook
 * 
 * 提供React组件的可访问性功能集成
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { accessibilityManager, KeyboardShortcut } from './AccessibilityManager';

export interface UseAccessibilityOptions {
  enableFocusManagement?: boolean;
  enableKeyboardShortcuts?: boolean;
  enableAnnouncements?: boolean;
  role?: string;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  tabIndex?: number;
}

export interface AccessibilityState {
  isFocused: boolean;
  isKeyboardUser: boolean;
  hasHighContrast: boolean;
  hasReducedMotion: boolean;
}

/**
 * 可访问性Hook
 */
export const useAccessibility = <T extends HTMLElement = HTMLElement>(options: UseAccessibilityOptions = {}) => {
  const {
    enableFocusManagement = true,
    enableKeyboardShortcuts = true,
    enableAnnouncements = true,
    role,
    ariaLabel,
    ariaDescribedBy,
    tabIndex,
  } = options;

  const elementRef = useRef<T>(null);
  const [state, setState] = useState<AccessibilityState>({
    isFocused: false,
    isKeyboardUser: false,
    hasHighContrast: false,
    hasReducedMotion: false,
  });

  // 初始化可访问性管理器
  useEffect(() => {
    accessibilityManager.initialize();
  }, []);

  // 设置元素属性
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    if (role) element.setAttribute('role', role);
    if (ariaLabel) element.setAttribute('aria-label', ariaLabel);
    if (ariaDescribedBy) element.setAttribute('aria-describedby', ariaDescribedBy);
    if (tabIndex !== undefined) element.setAttribute('tabindex', tabIndex.toString());
  }, [role, ariaLabel, ariaDescribedBy, tabIndex]);

  // 监听用户偏好变化
  useEffect(() => {
    const updatePreferences = () => {
      setState(prev => ({
        ...prev,
        hasHighContrast: document.documentElement.getAttribute('data-high-contrast') === 'true',
        hasReducedMotion: document.documentElement.getAttribute('data-reduced-motion') === 'true',
      }));
    };

    updatePreferences();

    const observer = new MutationObserver(updatePreferences);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-high-contrast', 'data-reduced-motion'],
    });

    return () => observer.disconnect();
  }, []);

  // 焦点管理
  useEffect(() => {
    if (!enableFocusManagement) return;

    const element = elementRef.current;
    if (!element) return;

    const handleFocus = () => {
      setState(prev => ({ ...prev, isFocused: true }));
    };

    const handleBlur = () => {
      setState(prev => ({ ...prev, isFocused: false }));
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        setState(prev => ({ ...prev, isKeyboardUser: true }));
      }
    };

    const handleMouseDown = () => {
      setState(prev => ({ ...prev, isKeyboardUser: false }));
    };

    element.addEventListener('focus', handleFocus);
    element.addEventListener('blur', handleBlur);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      element.removeEventListener('focus', handleFocus);
      element.removeEventListener('blur', handleBlur);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [enableFocusManagement]);

  // 播报消息
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (enableAnnouncements) {
      accessibilityManager.announce(message, priority);
    }
  }, [enableAnnouncements]);

  // 注册快捷键
  const registerShortcut = useCallback((shortcut: KeyboardShortcut) => {
    if (enableKeyboardShortcuts) {
      accessibilityManager.registerShortcut(shortcut);
    }
  }, [enableKeyboardShortcuts]);

  // 设置焦点
  const focus = useCallback(() => {
    if (elementRef.current) {
      elementRef.current.focus();
    }
  }, []);

  return {
    elementRef,
    state,
    announce,
    registerShortcut,
    focus,
  };
};

/**
 * 键盘导航Hook
 */
export const useKeyboardNavigation = (
  items: HTMLElement[] | (() => HTMLElement[]),
  options: {
    orientation?: 'horizontal' | 'vertical' | 'both';
    wrap?: boolean;
    autoFocus?: boolean;
  } = {}
) => {
  const { orientation = 'vertical', wrap = true, autoFocus = false } = options;
  const [currentIndex, setCurrentIndex] = useState(autoFocus ? 0 : -1);
  const containerRef = useRef<HTMLElement>(null);

  const getItems = useCallback(() => {
    return typeof items === 'function' ? items() : items;
  }, [items]);

  const focusItem = useCallback((index: number) => {
    const itemList = getItems();
    if (index >= 0 && index < itemList.length) {
      itemList[index].focus();
      setCurrentIndex(index);
    }
  }, [getItems]);

  const moveNext = useCallback(() => {
    const itemList = getItems();
    const nextIndex = currentIndex + 1;
    
    if (nextIndex < itemList.length) {
      focusItem(nextIndex);
    } else if (wrap) {
      focusItem(0);
    }
  }, [currentIndex, focusItem, getItems, wrap]);

  const movePrevious = useCallback(() => {
    const itemList = getItems();
    const prevIndex = currentIndex - 1;
    
    if (prevIndex >= 0) {
      focusItem(prevIndex);
    } else if (wrap) {
      focusItem(itemList.length - 1);
    }
  }, [currentIndex, focusItem, getItems, wrap]);

  const moveFirst = useCallback(() => {
    focusItem(0);
  }, [focusItem]);

  const moveLast = useCallback(() => {
    const itemList = getItems();
    focusItem(itemList.length - 1);
  }, [focusItem, getItems]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const { key } = event;
      
      switch (key) {
        case 'ArrowDown':
          if (orientation === 'vertical' || orientation === 'both') {
            event.preventDefault();
            moveNext();
          }
          break;
        case 'ArrowUp':
          if (orientation === 'vertical' || orientation === 'both') {
            event.preventDefault();
            movePrevious();
          }
          break;
        case 'ArrowRight':
          if (orientation === 'horizontal' || orientation === 'both') {
            event.preventDefault();
            moveNext();
          }
          break;
        case 'ArrowLeft':
          if (orientation === 'horizontal' || orientation === 'both') {
            event.preventDefault();
            movePrevious();
          }
          break;
        case 'Home':
          event.preventDefault();
          moveFirst();
          break;
        case 'End':
          event.preventDefault();
          moveLast();
          break;
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [orientation, moveNext, movePrevious, moveFirst, moveLast]);

  return {
    containerRef,
    currentIndex,
    focusItem,
    moveNext,
    movePrevious,
    moveFirst,
    moveLast,
  };
};

/**
 * ARIA状态管理Hook
 */
export const useAriaState = (initialState: Record<string, any> = {}) => {
  const [ariaState, setAriaState] = useState(initialState);
  const elementRef = useRef<HTMLElement>(null);

  const updateAriaState = useCallback((updates: Record<string, any>) => {
    setAriaState(prev => ({ ...prev, ...updates }));
  }, []);

  // 应用ARIA属性到元素
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    Object.entries(ariaState).forEach(([key, value]) => {
      const ariaKey = key.startsWith('aria-') ? key : `aria-${key}`;
      
      if (value === null || value === undefined) {
        element.removeAttribute(ariaKey);
      } else {
        element.setAttribute(ariaKey, String(value));
      }
    });
  }, [ariaState]);

  return {
    elementRef,
    ariaState,
    updateAriaState,
  };
};

/**
 * 焦点陷阱Hook
 */
export const useFocusTrap = (isActive: boolean = false) => {
  const containerRef = useRef<HTMLElement>(null);
  const firstFocusableRef = useRef<HTMLElement | null>(null);
  const lastFocusableRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isActive) return;

    const container = containerRef.current;
    if (!container) return;

    // 获取所有可聚焦元素
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    firstFocusableRef.current = firstElement;
    lastFocusableRef.current = lastElement;

    // 聚焦第一个元素
    firstElement.focus();

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isActive]);

  return {
    containerRef,
    firstFocusableRef,
    lastFocusableRef,
  };
};

/**
 * 屏幕阅读器专用内容Hook
 */
export const useScreenReaderOnly = () => {
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 应用屏幕阅读器专用样式
    element.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
    `;
  }, []);

  return { elementRef };
};
