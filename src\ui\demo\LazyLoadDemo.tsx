/**
 * 懒加载演示页面
 * 
 * 展示代码分割和懒加载功能的效果
 */

import React, { useState, useCallback } from 'react';
import { useThemeStyles } from '../theme/ThemeProvider';
import { Button } from '../components/Button';
import { 
  LazyLoader, 
  useLazyLoad, 
  createLazyModule, 
  LazyImage,
  useLazyLoadPerformance 
} from '../utils/LazyLoader';

// 懒加载的重量级组件
const LazyCodeEditor = LazyLoader(
  () => import('../components/lazy/LazyCodeEditor').then(module => ({ default: module.LazyCodeEditor })),
  {
    chunkName: 'code-editor',
    preload: false,
    retryCount: 3,
  }
);

const LazyChart = LazyLoader(
  () => import('../components/lazy/LazyChart').then(module => ({ default: module.LazyChart })),
  {
    chunkName: 'chart',
    preload: false,
    retryCount: 3,
  }
);

// 懒加载的工具模块
const mathUtils = createLazyModule(
  () => import('../utils/mathUtils').then(module => module),
  {
    preload: false,
    cache: true,
    cacheKey: 'math-utils',
  }
);

export const LazyLoadDemo: React.FC = () => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const { metrics, recordChunkLoad } = useLazyLoadPerformance();
  
  const [showCodeEditor, setShowCodeEditor] = useState(false);
  const [showChart, setShowChart] = useState(false);
  const [codeValue, setCodeValue] = useState(`// AI编程助手代码示例
function fibonacci(n: number): number {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

// 使用示例
console.log(fibonacci(10)); // 55`);

  // 懒加载数学工具
  const { data: mathUtilsData, loading: mathLoading, error: mathError, reload: reloadMath } = useLazyLoad(
    () => mathUtils.load(),
    []
  );

  // 图表数据
  const chartData = {
    labels: ['一月', '二月', '三月', '四月', '五月', '六月'],
    datasets: [
      {
        label: '用户增长',
        data: [12, 19, 3, 5, 2, 3],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
      },
      {
        label: '收入增长',
        data: [2, 3, 20, 5, 1, 4],
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
      },
    ],
  };

  const containerStyles: React.CSSProperties = {
    padding: spacing.lg,
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: typography.fontFamily,
    color: colors.text,
    backgroundColor: colors.background,
    minHeight: '100vh',
  };

  const sectionStyles: React.CSSProperties = {
    marginBottom: spacing.xl,
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.border}`,
  };

  const titleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.md,
    color: colors.text,
  };

  const subtitleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
    color: colors.text,
  };

  const metricsStyles: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
    gap: spacing.md,
    marginBottom: spacing.lg,
  };

  const metricCardStyles: React.CSSProperties = {
    padding: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.md,
    border: `1px solid ${colors.border}`,
    textAlign: 'center',
  };

  const handleLoadCodeEditor = useCallback(() => {
    const startTime = performance.now();
    setShowCodeEditor(true);
    
    // 模拟记录加载时间
    setTimeout(() => {
      const loadTime = performance.now() - startTime;
      recordChunkLoad(loadTime, true);
    }, 100);
  }, [recordChunkLoad]);

  const handleLoadChart = useCallback(() => {
    const startTime = performance.now();
    setShowChart(true);
    
    // 模拟记录加载时间
    setTimeout(() => {
      const loadTime = performance.now() - startTime;
      recordChunkLoad(loadTime, true);
    }, 100);
  }, [recordChunkLoad]);

  const handleUseMathUtils = useCallback(() => {
    if (mathUtilsData) {
      // 使用数学工具
      const result = mathUtilsData.fibonacci(10);
      alert(`斐波那契数列第10项: ${result}`);
    }
  }, [mathUtilsData]);

  return (
    <div style={containerStyles}>
      <h1 style={titleStyles}>AI编程助手 - 懒加载演示</h1>
      
      {/* 性能指标 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>懒加载性能指标</h2>
        <div style={metricsStyles}>
          <div style={metricCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.primary }}>
              {metrics.totalChunks}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              总块数
            </div>
          </div>
          
          <div style={metricCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.success }}>
              {metrics.loadedChunks}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              已加载
            </div>
          </div>
          
          <div style={metricCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.error }}>
              {metrics.failedChunks}
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              加载失败
            </div>
          </div>
          
          <div style={metricCardStyles}>
            <div style={{ fontSize: typography.fontSize.lg, fontWeight: 'bold', color: colors.info }}>
              {metrics.averageLoadTime.toFixed(0)}ms
            </div>
            <div style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              平均加载时间
            </div>
          </div>
        </div>
      </section>

      {/* 懒加载组件演示 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>懒加载组件</h2>
        <p style={{ color: colors.textSecondary, marginBottom: spacing.md }}>
          这些重量级组件只有在需要时才会加载，减少初始包大小。
        </p>
        
        <div style={{ display: 'flex', gap: spacing.md, marginBottom: spacing.lg, flexWrap: 'wrap' }}>
          <Button 
            onClick={handleLoadCodeEditor}
            disabled={showCodeEditor}
            variant={showCodeEditor ? 'secondary' : 'primary'}
          >
            {showCodeEditor ? '代码编辑器已加载' : '加载代码编辑器'}
          </Button>
          
          <Button 
            onClick={handleLoadChart}
            disabled={showChart}
            variant={showChart ? 'secondary' : 'primary'}
          >
            {showChart ? '图表组件已加载' : '加载图表组件'}
          </Button>
        </div>

        {showCodeEditor && (
          <div style={{ marginBottom: spacing.lg }}>
            <h3 style={subtitleStyles}>代码编辑器</h3>
            <LazyCodeEditor
              value={codeValue}
              language="typescript"
              onChange={setCodeValue}
              height={300}
            />
          </div>
        )}

        {showChart && (
          <div>
            <h3 style={subtitleStyles}>数据图表</h3>
            <LazyChart
              type="bar"
              data={chartData}
              height={300}
              options={{
                plugins: {
                  title: {
                    display: true,
                    text: '业务数据统计',
                  },
                },
              }}
            />
          </div>
        )}
      </section>

      {/* 懒加载模块演示 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>懒加载模块</h2>
        <p style={{ color: colors.textSecondary, marginBottom: spacing.md }}>
          工具模块按需加载，提高应用启动速度。
        </p>
        
        <div style={{ marginBottom: spacing.md }}>
          <Button 
            onClick={reloadMath}
            disabled={mathLoading}
            variant="outline"
            style={{ marginRight: spacing.sm }}
          >
            {mathLoading ? '加载中...' : '加载数学工具'}
          </Button>
          
          <Button 
            onClick={handleUseMathUtils}
            disabled={!mathUtilsData || mathLoading}
            variant="primary"
          >
            使用斐波那契函数
          </Button>
        </div>
        
        {mathError && (
          <div style={{ 
            color: colors.error, 
            fontSize: typography.fontSize.sm,
            marginBottom: spacing.md 
          }}>
            加载失败: {mathError.message}
          </div>
        )}
        
        {mathUtilsData && (
          <div style={{ 
            color: colors.success, 
            fontSize: typography.fontSize.sm 
          }}>
            ✅ 数学工具模块已加载
          </div>
        )}
      </section>

      {/* 懒加载图片演示 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>懒加载图片</h2>
        <p style={{ color: colors.textSecondary, marginBottom: spacing.md }}>
          图片只有在进入视口时才会加载，节省带宽。
        </p>
        
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: spacing.md 
        }}>
          {[1, 2, 3, 4].map((id) => (
            <div key={id} style={{
              backgroundColor: colors.background,
              borderRadius: borderRadius.md,
              overflow: 'hidden',
              border: `1px solid ${colors.border}`,
            }}>
              <LazyImage
                src={`https://picsum.photos/300/200?random=${id}`}
                placeholder={`data:image/svg+xml;base64,${btoa(`
                  <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="${colors.surface}"/>
                    <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="${colors.textSecondary}">
                      加载中...
                    </text>
                  </svg>
                `)}`}
                alt={`示例图片 ${id}`}
                style={{
                  width: '100%',
                  height: '200px',
                  objectFit: 'cover',
                }}
                onLoad={() => console.log(`图片 ${id} 加载完成`)}
                onError={() => console.log(`图片 ${id} 加载失败`)}
              />
              <div style={{ padding: spacing.sm }}>
                <h4 style={{ margin: 0, fontSize: typography.fontSize.sm }}>
                  示例图片 {id}
                </h4>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 优化建议 */}
      <section style={sectionStyles}>
        <h2 style={subtitleStyles}>优化建议</h2>
        <ul style={{ color: colors.textSecondary, lineHeight: 1.6 }}>
          <li>使用代码分割将大型组件拆分为独立的块</li>
          <li>为重要组件启用预加载，提升用户体验</li>
          <li>实现错误边界和重试机制，提高可靠性</li>
          <li>监控懒加载性能，优化加载策略</li>
          <li>使用图片懒加载减少初始页面大小</li>
          <li>缓存已加载的模块，避免重复加载</li>
        </ul>
      </section>
    </div>
  );
};
