/**
 * 懒加载的图表组件
 * 
 * 使用Chart.js的懒加载图表组件，优化包大小
 */

import React, { useState, useEffect, useRef } from 'react';
import { useThemeStyles } from '../../theme/ThemeProvider';

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
  }[];
}

export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      display?: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    title?: {
      display?: boolean;
      text?: string;
    };
  };
  scales?: {
    x?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
    y?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
  };
}

export interface LazyChartProps {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'radar' | 'polarArea';
  data: ChartData;
  options?: ChartOptions;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
}

export const LazyChart: React.FC<LazyChartProps> = ({
  type,
  data,
  options = {},
  width,
  height = 400,
  className,
  style,
}) => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const [Chart, setChart] = useState<any>(null);
  const [chartInstance, setChartInstance] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 动态加载Chart.js
  useEffect(() => {
    let isMounted = true;

    const loadChart = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 动态导入Chart.js
        const chartModule = await import('chart.js/auto');
        
        if (!isMounted) return;

        setChart(chartModule.default);
        setIsLoading(false);
      } catch (err) {
        if (!isMounted) return;
        
        console.error('Failed to load Chart.js:', err);
        setError('图表库加载失败');
        setIsLoading(false);
      }
    };

    loadChart();

    return () => {
      isMounted = false;
    };
  }, []);

  // 创建图表实例
  useEffect(() => {
    if (!Chart || !canvasRef.current || chartInstance) return;

    try {
      const ctx = canvasRef.current.getContext('2d');
      if (!ctx) return;

      // 应用主题颜色
      const themedData = {
        ...data,
        datasets: data.datasets.map((dataset, index) => ({
          ...dataset,
          backgroundColor: dataset.backgroundColor || getThemeColor(index, 0.6),
          borderColor: dataset.borderColor || getThemeColor(index, 1),
          borderWidth: dataset.borderWidth || 2,
        })),
      };

      // 应用主题选项
      const themedOptions = {
        responsive: true,
        maintainAspectRatio: false,
        ...options,
        plugins: {
          ...options.plugins,
          legend: {
            display: true,
            position: 'top' as const,
            ...options.plugins?.legend,
            labels: {
              color: colors.text,
              font: {
                family: typography.fontFamily,
                size: 12,
              },
              ...options.plugins?.legend?.labels,
            },
          },
          title: {
            ...options.plugins?.title,
            color: colors.text,
            font: {
              family: typography.fontFamily,
              size: 16,
              weight: 'bold',
            },
          },
        },
        scales: type === 'pie' || type === 'doughnut' ? undefined : {
          x: {
            display: true,
            ...options.scales?.x,
            ticks: {
              color: colors.textSecondary,
              font: {
                family: typography.fontFamily,
                size: 11,
              },
              ...options.scales?.x?.ticks,
            },
            grid: {
              color: colors.border,
              ...options.scales?.x?.grid,
            },
            title: {
              ...options.scales?.x?.title,
              color: colors.text,
              font: {
                family: typography.fontFamily,
                size: 12,
                weight: 'bold',
              },
            },
          },
          y: {
            display: true,
            ...options.scales?.y,
            ticks: {
              color: colors.textSecondary,
              font: {
                family: typography.fontFamily,
                size: 11,
              },
              ...options.scales?.y?.ticks,
            },
            grid: {
              color: colors.border,
              ...options.scales?.y?.grid,
            },
            title: {
              ...options.scales?.y?.title,
              color: colors.text,
              font: {
                family: typography.fontFamily,
                size: 12,
                weight: 'bold',
              },
            },
          },
        },
      };

      const instance = new Chart(ctx, {
        type,
        data: themedData,
        options: themedOptions,
      });

      setChartInstance(instance);
    } catch (err) {
      console.error('Failed to create chart instance:', err);
      setError('图表初始化失败');
    }
  }, [Chart, type, data, options, colors, typography]);

  // 更新图表数据
  useEffect(() => {
    if (chartInstance) {
      chartInstance.data = {
        ...data,
        datasets: data.datasets.map((dataset, index) => ({
          ...dataset,
          backgroundColor: dataset.backgroundColor || getThemeColor(index, 0.6),
          borderColor: dataset.borderColor || getThemeColor(index, 1),
        })),
      };
      chartInstance.update();
    }
  }, [chartInstance, data]);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance) {
        chartInstance.destroy();
      }
    };
  }, [chartInstance]);

  // 获取主题颜色
  const getThemeColor = (index: number, alpha: number = 1): string => {
    const colorPalette = [
      colors.primary,
      colors.success,
      colors.warning,
      colors.error,
      colors.info,
      colors.accent,
    ];
    
    const color = colorPalette[index % colorPalette.length];
    
    // 如果需要透明度，转换为rgba
    if (alpha < 1) {
      const rgb = hexToRgb(color);
      if (rgb) {
        return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
      }
    }
    
    return color;
  };

  // 十六进制转RGB
  const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null;
  };

  const containerStyles: React.CSSProperties = {
    width: width || '100%',
    height: height,
    backgroundColor: themeConfig.colors.surface,
    border: `1px solid ${themeConfig.colors.border}`,
    borderRadius: themeConfig.borderRadius.md,
    padding: themeConfig.spacing.md,
    ...style,
  };

  const loadingStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    color: themeConfig.colors.textSecondary,
    fontSize: themeConfig.typography.fontSize.sm,
  };

  const errorStyles: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    color: themeConfig.colors.error,
    fontSize: themeConfig.typography.fontSize.sm,
    textAlign: 'center',
  };

  if (error) {
    return (
      <div className={className} style={containerStyles}>
        <div style={errorStyles}>
          <div style={{ marginBottom: themeConfig.spacing.sm }}>📊</div>
          <div>{error}</div>
          <button
            onClick={() => window.location.reload()}
            style={{
              marginTop: themeConfig.spacing.md,
              padding: `${themeConfig.spacing.sm} ${themeConfig.spacing.md}`,
              backgroundColor: themeConfig.colors.primary,
              color: 'white',
              border: 'none',
              borderRadius: themeConfig.borderRadius.sm,
              cursor: 'pointer',
              fontSize: themeConfig.typography.fontSize.sm,
            }}
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={className} style={containerStyles}>
        <div style={loadingStyles}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: themeConfig.spacing.sm,
          }}>
            <div style={{
              width: '16px',
              height: '16px',
              border: `2px solid ${themeConfig.colors.border}`,
              borderTop: `2px solid ${themeConfig.colors.primary}`,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }} />
            正在加载图表...
          </div>
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}
          </style>
        </div>
      </div>
    );
  }

  return (
    <div className={className} style={containerStyles}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
        }}
      />
    </div>
  );
};
