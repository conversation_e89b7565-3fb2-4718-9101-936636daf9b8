/**
 * Semantic Embedding Generator - 语义嵌入生成器
 * 
 * 使用@xenova/transformers库生成代码的语义嵌入向量
 */

import { EventBus } from '../core/EventBus';

export interface EmbeddingConfig {
  model: string;
  maxLength: number;
  batchSize: number;
  cacheSize: number;
}

export interface EmbeddingResult {
  embeddings: number[];
  model: string;
  timestamp: Date;
  inputLength: number;
}

export class SemanticEmbeddingGenerator {
  private eventBus: EventBus;
  private pipeline: any = null;
  private isInitialized = false;
  private config: EmbeddingConfig;
  private embeddingCache: Map<string, EmbeddingResult> = new Map();
  private transformersModule: any = null;

  constructor(eventBus: EventBus, config?: Partial<EmbeddingConfig>) {
    this.eventBus = eventBus;
    this.config = {
      model: 'Xenova/codebert-base',
      maxLength: 512,
      batchSize: 8,
      cacheSize: 1000,
      ...config
    };
  }

  /**
   * 动态加载@xenova/transformers模块
   */
  private async loadTransformersModule(): Promise<any> {
    if (this.transformersModule) {
      return this.transformersModule;
    }

    try {
      // 尝试动态导入@xenova/transformers
      this.transformersModule = await import('@xenova/transformers');
      return this.transformersModule;
    } catch (error) {
      console.warn('Failed to load @xenova/transformers:', error);

      // 返回一个模拟的pipeline函数作为降级方案
      return {
        pipeline: async (task: string, model: string, options?: any) => {
          console.warn(`Mock pipeline for ${task} with model ${model}`);
          return {
            // 模拟的特征提取函数
            async call(texts: string | string[]): Promise<any> {
              const textArray = Array.isArray(texts) ? texts : [texts];
              // 返回模拟的嵌入向量（768维，CodeBERT的标准维度）
              return textArray.map(() => Array(768).fill(0).map(() => Math.random() - 0.5));
            }
          };
        }
      };
    }
  }

  /**
   * 初始化语义嵌入模型
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.eventBus.emit({
        type: 'embedding_generator.initializing',
        source: 'SemanticEmbeddingGenerator',
        model: this.config.model
      });

      // 动态加载transformers模块并创建特征提取管道
      const transformers = await this.loadTransformersModule();
      this.pipeline = await transformers.pipeline('feature-extraction', this.config.model, {
        quantized: true, // 使用量化模型以减少内存使用
        progress_callback: (progress: any) => {
          this.eventBus.emit({
            type: 'embedding_generator.loading_progress',
            source: 'SemanticEmbeddingGenerator',
            progress: progress.progress || 0,
            status: progress.status || 'loading'
          });
        }
      });

      this.isInitialized = true;

      this.eventBus.emit({
        type: 'embedding_generator.initialized',
        source: 'SemanticEmbeddingGenerator',
        model: this.config.model
      });
    } catch (error) {
      this.eventBus.emit({
        type: 'embedding_generator.initialization_failed',
        source: 'SemanticEmbeddingGenerator',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 生成代码的语义嵌入
   */
  async generateEmbedding(code: string, useCache = true): Promise<EmbeddingResult> {
    if (!this.isInitialized || !this.pipeline) {
      await this.initialize();
    }

    // 检查缓存
    const cacheKey = this.getCacheKey(code);
    if (useCache && this.embeddingCache.has(cacheKey)) {
      return this.embeddingCache.get(cacheKey)!;
    }

    try {
      // 预处理代码文本
      const processedCode = this.preprocessCode(code);
      
      // 生成嵌入
      const startTime = Date.now();
      const output = await this.pipeline!(processedCode, {
        pooling: 'mean',
        normalize: true
      });

      const embeddings = Array.from(output.data) as number[];
      const processingTime = Date.now() - startTime;

      const result: EmbeddingResult = {
        embeddings,
        model: this.config.model,
        timestamp: new Date(),
        inputLength: processedCode.length
      };

      // 缓存结果
      if (useCache) {
        this.cacheEmbedding(cacheKey, result);
      }

      this.eventBus.emit({
        type: 'embedding_generator.embedding_generated',
        source: 'SemanticEmbeddingGenerator',
        inputLength: processedCode.length,
        outputDimension: embeddings.length,
        processingTime
      });

      return result;
    } catch (error) {
      this.eventBus.emit({
        type: 'embedding_generator.generation_failed',
        source: 'SemanticEmbeddingGenerator',
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * 批量生成嵌入
   */
  async generateBatchEmbeddings(codes: string[]): Promise<EmbeddingResult[]> {
    const results: EmbeddingResult[] = [];
    
    // 分批处理
    for (let i = 0; i < codes.length; i += this.config.batchSize) {
      const batch = codes.slice(i, i + this.config.batchSize);
      const batchPromises = batch.map(code => this.generateEmbedding(code));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 发送进度事件
      this.eventBus.emit({
        type: 'embedding_generator.batch_progress',
        source: 'SemanticEmbeddingGenerator',
        processed: i + batch.length,
        total: codes.length,
        progress: ((i + batch.length) / codes.length) * 100
      });
    }

    return results;
  }

  /**
   * 计算两个嵌入向量的余弦相似度
   */
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embedding dimensions must match');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  /**
   * 预处理代码文本
   */
  private preprocessCode(code: string): string {
    // 移除过长的注释
    let processed = code.replace(/\/\*[\s\S]*?\*\//g, '/* comment */');
    processed = processed.replace(/\/\/.*$/gm, '// comment');
    
    // 移除多余的空白字符
    processed = processed.replace(/\s+/g, ' ').trim();
    
    // 截断到最大长度
    if (processed.length > this.config.maxLength) {
      processed = processed.substring(0, this.config.maxLength);
    }
    
    return processed;
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(code: string): string {
    // 使用简单的哈希函数生成缓存键
    let hash = 0;
    for (let i = 0; i < code.length; i++) {
      const char = code.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `${this.config.model}_${hash.toString(36)}`;
  }

  /**
   * 缓存嵌入结果
   */
  private cacheEmbedding(key: string, result: EmbeddingResult): void {
    // 如果缓存已满，删除最旧的条目
    if (this.embeddingCache.size >= this.config.cacheSize) {
      const firstKey = this.embeddingCache.keys().next().value;
      this.embeddingCache.delete(firstKey);
    }
    
    this.embeddingCache.set(key, result);
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.embeddingCache.clear();
    this.eventBus.emit({
      type: 'embedding_generator.cache_cleared',
      source: 'SemanticEmbeddingGenerator'
    });
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      size: this.embeddingCache.size,
      maxSize: this.config.cacheSize,
      hitRate: 0, // TODO: 实现命中率统计
      isInitialized: this.isInitialized,
      model: this.config.model
    };
  }

  /**
   * 检查模型是否可用
   */
  isModelAvailable(): boolean {
    return this.isInitialized && this.pipeline !== null;
  }

  /**
   * 获取嵌入维度
   */
  async getEmbeddingDimension(): Promise<number> {
    if (!this.isModelAvailable()) {
      await this.initialize();
    }
    
    // 使用简单的测试代码获取维度
    const testResult = await this.generateEmbedding('function test() {}', false);
    return testResult.embeddings.length;
  }

  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    this.clearCache();
    this.pipeline = null;
    this.isInitialized = false;
    
    this.eventBus.emit({
      type: 'embedding_generator.disposed',
      source: 'SemanticEmbeddingGenerator'
    });
  }
}
