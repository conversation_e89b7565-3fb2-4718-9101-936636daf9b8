/**
 * 可访问性演示页面
 * 
 * 展示键盘导航、屏幕阅读器支持和ARIA标签功能
 */

import React, { useState, useRef } from 'react';
import { useThemeStyles } from '../theme/ThemeProvider';
import { Button } from '../components/Button';
import { ThemeSelector } from '../components/ThemeSelector';
import { 
  useAccessibility, 
  useKeyboardNavigation, 
  useAriaState, 
  useFocusTrap,
  useScreenReaderOnly 
} from '../accessibility/useAccessibility';
import { accessibilityManager } from '../accessibility/AccessibilityManager';

export const AccessibilityDemo: React.FC = () => {
  const { colors, spacing, typography, borderRadius } = useThemeStyles();
  const [showModal, setShowModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [notifications, setNotifications] = useState<string[]>([]);

  // 主要内容区域的可访问性
  const { elementRef: mainRef, announce } = useAccessibility<HTMLDivElement>({
    enableFocusManagement: true,
    enableAnnouncements: true,
    role: 'main',
    ariaLabel: '可访问性演示主要内容',
  });

  // 屏幕阅读器专用内容
  const { elementRef: srOnlyRef } = useScreenReaderOnly();

  // 模态框焦点陷阱
  const { containerRef: modalRef } = useFocusTrap(showModal);

  // 标签导航
  const tabs = ['基础功能', '键盘导航', '屏幕阅读器', '高对比度'];
  const tabRefs = useRef<HTMLButtonElement[]>([]);
  const { containerRef: tabListRef } = useKeyboardNavigation(
    () => tabRefs.current.filter(Boolean),
    { orientation: 'horizontal', wrap: true }
  );

  // 按钮列表导航
  const buttonRefs = useRef<HTMLButtonElement[]>([]);
  const { containerRef: buttonListRef } = useKeyboardNavigation(
    () => buttonRefs.current.filter(Boolean),
    { orientation: 'vertical', wrap: true }
  );

  // ARIA状态管理
  const { elementRef: alertRef, updateAriaState } = useAriaState({
    live: 'polite',
    atomic: true,
  });

  const containerStyles: React.CSSProperties = {
    padding: spacing.lg,
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: typography.fontFamily,
    color: colors.text,
    backgroundColor: colors.background,
    minHeight: '100vh',
  };

  const sectionStyles: React.CSSProperties = {
    marginBottom: spacing.xl,
    padding: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.border}`,
  };

  const titleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.md,
    color: colors.text,
  };

  const subtitleStyles: React.CSSProperties = {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    marginBottom: spacing.sm,
    color: colors.text,
  };

  const tabStyles = (isActive: boolean): React.CSSProperties => ({
    padding: `${spacing.sm} ${spacing.md}`,
    border: `2px solid ${isActive ? colors.primary : colors.border}`,
    backgroundColor: isActive ? colors.primary : 'transparent',
    color: isActive ? '#ffffff' : colors.text,
    borderRadius: `${borderRadius.md} ${borderRadius.md} 0 0`,
    cursor: 'pointer',
    fontSize: typography.fontSize.sm,
    fontWeight: isActive ? typography.fontWeight.medium : typography.fontWeight.normal,
    minHeight: '44px',
    minWidth: '120px',
  });

  const tabPanelStyles: React.CSSProperties = {
    padding: spacing.lg,
    border: `2px solid ${colors.border}`,
    borderTop: 'none',
    backgroundColor: colors.background,
    borderRadius: `0 0 ${borderRadius.md} ${borderRadius.md}`,
    minHeight: '200px',
  };

  const modalStyles: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  };

  const modalContentStyles: React.CSSProperties = {
    backgroundColor: colors.surface,
    padding: spacing.xl,
    borderRadius: borderRadius.lg,
    maxWidth: '500px',
    width: '90%',
    maxHeight: '80vh',
    overflow: 'auto',
  };

  const handleTabChange = (index: number) => {
    setSelectedTab(index);
    announce(`切换到${tabs[index]}标签页`);
  };

  const handleNotification = (message: string) => {
    setNotifications(prev => [...prev, message]);
    announce(message, 'assertive');
    updateAriaState({ live: 'assertive' });
    
    // 3秒后移除通知
    setTimeout(() => {
      setNotifications(prev => prev.slice(1));
    }, 3000);
  };

  const handleKeyboardShortcut = () => {
    accessibilityManager.registerShortcut({
      key: 'KeyD',
      ctrlKey: true,
      action: () => handleNotification('演示快捷键已触发'),
      description: '演示快捷键',
    });
    handleNotification('快捷键 Ctrl+D 已注册');
  };

  const handleToggleHighContrast = () => {
    const currentState = document.documentElement.getAttribute('data-high-contrast') === 'true';
    document.documentElement.setAttribute('data-high-contrast', (!currentState).toString());
    handleNotification(`高对比度模式已${!currentState ? '启用' : '禁用'}`);
  };

  const renderTabPanel = () => {
    switch (selectedTab) {
      case 0:
        return (
          <div>
            <h3 style={subtitleStyles}>基础可访问性功能</h3>
            <p style={{ marginBottom: spacing.md, color: colors.textSecondary }}>
              这个演示展示了基本的可访问性功能，包括语义化HTML、ARIA标签和键盘导航。
            </p>
            <div style={{ display: 'flex', gap: spacing.sm, flexWrap: 'wrap' }}>
              <Button onClick={() => handleNotification('基础按钮被点击')}>
                基础按钮
              </Button>
              <Button 
                variant="secondary" 
                onClick={() => setShowModal(true)}
                aria-describedby="modal-description"
              >
                打开模态框
              </Button>
              <Button 
                variant="outline" 
                onClick={handleKeyboardShortcut}
              >
                注册快捷键
              </Button>
            </div>
            <p id="modal-description" style={{ 
              marginTop: spacing.sm, 
              fontSize: typography.fontSize.sm,
              color: colors.textSecondary 
            }}>
              点击此按钮将打开一个可访问的模态框
            </p>
          </div>
        );
      
      case 1:
        return (
          <div>
            <h3 style={subtitleStyles}>键盘导航演示</h3>
            <p style={{ marginBottom: spacing.md, color: colors.textSecondary }}>
              使用Tab键在元素间导航，使用方向键在按钮组中导航。
            </p>
            <div 
              ref={buttonListRef}
              style={{ display: 'flex', flexDirection: 'column', gap: spacing.sm, maxWidth: '200px' }}
              role="group"
              aria-label="键盘导航按钮组"
            >
              {['第一个', '第二个', '第三个', '第四个'].map((label, index) => (
                <Button
                  key={index}
                  ref={(el) => {
                    if (el) buttonRefs.current[index] = el;
                  }}
                  variant="outline"
                  onClick={() => handleNotification(`${label}按钮被点击`)}
                  aria-describedby="keyboard-nav-help"
                >
                  {label}按钮
                </Button>
              ))}
            </div>
            <p id="keyboard-nav-help" style={{ 
              marginTop: spacing.sm, 
              fontSize: typography.fontSize.sm,
              color: colors.textSecondary 
            }}>
              使用上下方向键在按钮间导航，Enter或空格键激活
            </p>
          </div>
        );
      
      case 2:
        return (
          <div>
            <h3 style={subtitleStyles}>屏幕阅读器支持</h3>
            <p style={{ marginBottom: spacing.md, color: colors.textSecondary }}>
              这些功能专为屏幕阅读器用户设计。
            </p>
            <div style={{ marginBottom: spacing.md }}>
              <span ref={srOnlyRef}>
                这段文字只对屏幕阅读器可见，视觉用户看不到。
              </span>
              <p>这段文字对所有用户都可见。</p>
            </div>
            <div 
              ref={alertRef}
              style={{ 
                padding: spacing.md,
                backgroundColor: colors.background,
                border: `1px solid ${colors.border}`,
                borderRadius: borderRadius.md,
                marginBottom: spacing.md,
              }}
            >
              <h4 style={{ margin: 0, marginBottom: spacing.sm }}>实时通知区域</h4>
              {notifications.length > 0 ? (
                <ul style={{ margin: 0, paddingLeft: spacing.md }}>
                  {notifications.map((notification, index) => (
                    <li key={index} style={{ marginBottom: spacing.xs }}>
                      {notification}
                    </li>
                  ))}
                </ul>
              ) : (
                <p style={{ margin: 0, color: colors.textSecondary }}>暂无通知</p>
              )}
            </div>
            <Button onClick={() => handleNotification('这是一条测试通知')}>
              发送测试通知
            </Button>
          </div>
        );
      
      case 3:
        return (
          <div>
            <h3 style={subtitleStyles}>高对比度模式</h3>
            <p style={{ marginBottom: spacing.md, color: colors.textSecondary }}>
              高对比度模式可以提高文本和界面元素的可读性。
            </p>
            <div style={{ marginBottom: spacing.md }}>
              <Button onClick={handleToggleHighContrast}>
                切换高对比度模式
              </Button>
            </div>
            <div style={{ marginBottom: spacing.md }}>
              <ThemeSelector 
                position="vertical" 
                showSyncOption={false}
                compact={false}
              />
            </div>
            <p style={{ fontSize: typography.fontSize.sm, color: colors.textSecondary }}>
              高对比度模式会覆盖主题设置，提供最佳的可读性。
            </p>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div style={containerStyles}>
      {/* 跳转链接 */}
      <a 
        href="#main-content" 
        className="skip-link"
        style={{
          position: 'absolute',
          top: '-40px',
          left: '6px',
          background: colors.primary,
          color: 'white',
          padding: '8px',
          textDecoration: 'none',
          borderRadius: '4px',
          zIndex: 10000,
          fontWeight: 'bold',
        }}
        onFocus={(e) => {
          e.currentTarget.style.top = '6px';
        }}
        onBlur={(e) => {
          e.currentTarget.style.top = '-40px';
        }}
      >
        跳转到主要内容
      </a>

      <header>
        <h1 style={titleStyles}>AI编程助手 - 可访问性演示</h1>
        <p style={{ color: colors.textSecondary, marginBottom: spacing.lg }}>
          这个页面演示了符合WCAG 2.1 AA级标准的可访问性功能
        </p>
      </header>

      <main id="main-content" ref={mainRef}>
        {/* 标签页导航 */}
        <section style={sectionStyles}>
          <h2 style={subtitleStyles}>功能演示</h2>
          
          <div 
            ref={tabListRef}
            role="tablist" 
            aria-label="可访问性功能演示"
            style={{ display: 'flex', marginBottom: 0 }}
          >
            {tabs.map((tab, index) => (
              <button
                key={index}
                ref={(el) => {
                  if (el) tabRefs.current[index] = el;
                }}
                role="tab"
                aria-selected={selectedTab === index}
                aria-controls={`tabpanel-${index}`}
                id={`tab-${index}`}
                tabIndex={selectedTab === index ? 0 : -1}
                style={tabStyles(selectedTab === index)}
                onClick={() => handleTabChange(index)}
              >
                {tab}
              </button>
            ))}
          </div>
          
          <div
            id={`tabpanel-${selectedTab}`}
            role="tabpanel"
            aria-labelledby={`tab-${selectedTab}`}
            style={tabPanelStyles}
          >
            {renderTabPanel()}
          </div>
        </section>

        {/* 快捷键帮助 */}
        <section style={sectionStyles}>
          <h2 style={subtitleStyles}>键盘快捷键</h2>
          <dl style={{ margin: 0 }}>
            <dt style={{ fontWeight: 'bold', marginBottom: spacing.xs }}>Tab / Shift+Tab</dt>
            <dd style={{ marginBottom: spacing.sm, marginLeft: spacing.md }}>在可聚焦元素间导航</dd>
            
            <dt style={{ fontWeight: 'bold', marginBottom: spacing.xs }}>方向键</dt>
            <dd style={{ marginBottom: spacing.sm, marginLeft: spacing.md }}>在按钮组或标签页间导航</dd>
            
            <dt style={{ fontWeight: 'bold', marginBottom: spacing.xs }}>Enter / 空格</dt>
            <dd style={{ marginBottom: spacing.sm, marginLeft: spacing.md }}>激活按钮或链接</dd>
            
            <dt style={{ fontWeight: 'bold', marginBottom: spacing.xs }}>Escape</dt>
            <dd style={{ marginBottom: spacing.sm, marginLeft: spacing.md }}>关闭模态框或下拉菜单</dd>
            
            <dt style={{ fontWeight: 'bold', marginBottom: spacing.xs }}>Ctrl+D</dt>
            <dd style={{ marginLeft: spacing.md }}>演示自定义快捷键</dd>
          </dl>
        </section>
      </main>

      {/* 模态框 */}
      {showModal && (
        <div style={modalStyles} onClick={() => setShowModal(false)}>
          <div 
            ref={modalRef}
            style={modalContentStyles}
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-title"
            aria-describedby="modal-description"
            onClick={(e) => e.stopPropagation()}
          >
            <header style={{ marginBottom: spacing.md }}>
              <h2 id="modal-title" style={{ margin: 0, fontSize: typography.fontSize.lg }}>
                可访问的模态框
              </h2>
            </header>
            <div id="modal-description" style={{ marginBottom: spacing.lg }}>
              <p>这是一个符合可访问性标准的模态框示例。它包含：</p>
              <ul>
                <li>焦点陷阱（Tab键只在模态框内循环）</li>
                <li>Escape键关闭功能</li>
                <li>适当的ARIA标签</li>
                <li>屏幕阅读器支持</li>
              </ul>
            </div>
            <div style={{ display: 'flex', gap: spacing.sm, justifyContent: 'flex-end' }}>
              <Button variant="outline" onClick={() => setShowModal(false)}>
                取消
              </Button>
              <Button onClick={() => {
                setShowModal(false);
                handleNotification('模态框操作已确认');
              }}>
                确认
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
